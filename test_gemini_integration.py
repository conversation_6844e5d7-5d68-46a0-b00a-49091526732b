#!/usr/bin/env python3
"""
Test script for Gemini API integration using the new google-genai SDK.
This script tests the basic functionality of the GeminiManager.
"""

from apis.llm.data_types import CompletionRequest
from apis.llm.gemini import GeminiManager
import os
import sys


# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_gemini_basic_functionality():
    """Test basic Gemini API functionality."""
    print("Testing Gemini API integration...")

    # Check if API key is set
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY environment variable not set")
        print("Please set your Gemini API key: export GEMINI_API_KEY='your-api-key'")
        print("Note: Using the new google-genai SDK")
        return False

    try:
        # Initialize the Gemini manager
        print("🔧 Initializing GeminiManager...")
        gemini_manager = GeminiManager(
            total_budget=1.0,  # $1 budget for testing
            total_cost=0.0,
            requests_per_minute=10
        )
        print("✅ GeminiManager initialized successfully")

        # Test single completion request
        print("🧪 Testing single completion request...")
        test_request = CompletionRequest(
            user_prompt="What is the capital of France? Please answer in one word.",
            system_prompt="You are a helpful assistant that provides concise answers.",
            model="gemini-2.0-flash",
            max_tokens=50,
            temperature=0.1
        )

        response = gemini_manager.get_completion(test_request)

        if response and response.status == "succeeded":
            print(f"✅ Single completion test passed")
            print(f"   Response: {response.content[:100]}...")
            print(f"   Input tokens: {response.input_tokens}")
            print(f"   Output tokens: {response.output_tokens}")
            print(f"   Cost: ${response.cost:.4f}")
        else:
            print(f"❌ Single completion test failed")
            if response:
                print(f"   Status: {response.status}")
                print(f"   Raw response: {response.raw_response}")
            return False

        # Test batch request creation
        print("🧪 Testing batch request creation...")
        batch_requests = [
            CompletionRequest(
                user_prompt="What is 2+2?",
                system_prompt="You are a math assistant.",
                model="gemini-2.0-flash",
                max_tokens=20,
                temperature=0.0,
                custom_id="math_1"
            ),
            CompletionRequest(
                user_prompt="What is the color of the sky?",
                system_prompt="You are a helpful assistant.",
                model="gemini-2.0-flash",
                max_tokens=20,
                temperature=0.0,
                custom_id="color_1"
            )
        ]

        batch_response = gemini_manager.get_completion_batch(batch_requests)

        if batch_response:
            print(f"✅ Batch creation test passed")
            print(f"   Batch ID: {batch_response.id}")
            print(f"   Status: {batch_response.status}")

            # Test batch status retrieval
            print("🧪 Testing batch status retrieval...")
            status_response = gemini_manager.retrieve_batch(batch_response.id)

            if status_response:
                print(f"✅ Batch status test passed")
                print(f"   Status: {status_response.status}")

                if status_response.completion_results:
                    print(
                        f"   Results count: {len(status_response.completion_results)}")
                    for i, result in enumerate(status_response.completion_results):
                        print(f"   Result {i+1}: {result.content[:50]}...")
                else:
                    print("   No results yet (batch may still be processing)")
            else:
                print("❌ Batch status test failed")
                return False
        else:
            print("❌ Batch creation test failed")
            return False

        print("🎉 All tests passed! Gemini integration is working correctly.")
        return True

    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_gemini_basic_functionality()
    sys.exit(0 if success else 1)
