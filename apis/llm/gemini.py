import json
import os
import time
import asyncio
from datetime import datetime, timezone
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

import google.generativeai as genai

from apis.llm.base import BaseAPIManager
from apis.llm.data_types import BatchResponse, BatchStatus, CompletionRequest, CompletionResponse, CompletionStatus
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, log_file='gemini.log')


class GeminiManager(BaseAPIManager):
    """Google Gemini API implementation."""

    def __init__(self, total_budget: float = 0.0, total_cost: float = 0.0, requests_per_minute: int = 30, **kwargs):
        model_pricing = {
            'gemini-2.0-flash': {
                'input_rate': 0.0001,  # $0.10 per 1M tokens
                'output_rate': 0.0004  # $0.40 per 1M tokens
            },
            'gemini-1.5-flash': {
                'input_rate': 0.000075,  # $0.075 per 1M tokens (<=128k)
                'output_rate': 0.0003    # $0.30 per 1M tokens (<=128k)
            },
            'gemini-1.5-pro': {
                'input_rate': 0.00125,   # $1.25 per 1M tokens (<=128k)
                'output_rate': 0.005     # $5.00 per 1M tokens (<=128k)
            },
            'gemini-1.5-flash-8b': {
                'input_rate': 0.0000375,  # $0.0375 per 1M tokens (<=128k)
                'output_rate': 0.00015   # $0.15 per 1M tokens (<=128k)
            }
        }
        super().__init__(total_budget=total_budget,
                         total_cost=total_cost,
                         requests_per_minute=requests_per_minute,
                         model_pricing=model_pricing,
                         batch_discount=0.8,  # Simulated batch discount for concurrent processing
                         **kwargs)
        self.client = self._initialize_client()

    def _initialize_client(self):
        """Initialize the Gemini client."""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set")

        genai.configure(api_key=api_key)
        return genai

    def _make_completion_request(self, request: CompletionRequest) -> CompletionResponse:
        """Make a single completion request to Gemini."""
        try:
            # Initialize the model
            model = genai.GenerativeModel(request.model)

            # Prepare the prompt
            if request.system_prompt:
                # Gemini doesn't have separate system prompts, so we prepend it to user prompt
                full_prompt = f"System: {request.system_prompt}\n\nUser: {request.user_prompt}"
            else:
                full_prompt = request.user_prompt

            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=request.max_tokens,
                temperature=request.temperature
            )

            # Make the request
            response = model.generate_content(
                full_prompt,
                generation_config=generation_config
            )

            # Extract response content
            content = response.text if response.text else ""

            # Get token usage (Gemini provides this in usage_metadata)
            input_tokens = response.usage_metadata.prompt_token_count if response.usage_metadata else 0
            output_tokens = response.usage_metadata.candidates_token_count if response.usage_metadata else 0

            return CompletionResponse(
                content=content,
                status=CompletionStatus.SUCCEEDED.value,
                model=request.model,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                raw_response=str(response)
            )

        except Exception as e:
            logger.error(f"Gemini completion request failed: {str(e)}")
            return CompletionResponse(
                content="",
                status=CompletionStatus.FAILED.value,
                model=request.model,
                input_tokens=0,
                output_tokens=0,
                raw_response=str(e)
            )

    def _create_batch_request(self, requests: List[CompletionRequest]) -> BatchResponse:
        """
        Create a simulated batch request using concurrent processing.
        Gemini doesn't have a native batch API, so we simulate it with async processing.
        """
        batch_id = f"gemini_batch_{int(time.time())}"

        # Store the requests for later processing
        self._pending_batches = getattr(self, '_pending_batches', {})
        self._pending_batches[batch_id] = {
            'requests': requests,
            'status': BatchStatus.IN_PROGRESS.value,
            'created_at': datetime.now(timezone.utc),
            'results': None
        }

        return BatchResponse(
            id=batch_id,
            status=BatchStatus.IN_PROGRESS.value,
            created_at=datetime.now(timezone.utc),
            # Will be updated when processing starts
            expires_at=datetime.now(timezone.utc),
            raw_response=f"Simulated batch request for {len(requests)} requests"
        )

    def _retrieve_batch_status_request(self, batch_id: str) -> BatchResponse:
        """Retrieve the status of a batch request."""
        pending_batches = getattr(self, '_pending_batches', {})

        if batch_id not in pending_batches:
            raise ValueError(f"Batch {batch_id} not found")

        batch_info = pending_batches[batch_id]

        # If still in progress, try to process it
        if batch_info['status'] == BatchStatus.IN_PROGRESS.value:
            self._process_batch_if_ready(batch_id)
            batch_info = pending_batches[batch_id]  # Refresh after processing

        return BatchResponse(
            id=batch_id,
            status=batch_info['status'],
            created_at=batch_info['created_at'],
            expires_at=batch_info.get(
                'completed_at', datetime.now(timezone.utc)),
            completed_at=batch_info.get('completed_at'),
            raw_response=f"Batch status: {batch_info['status']}"
        )

    def _retrieve_batch_results_request(self, batch_id: str) -> List[CompletionResponse]:
        """Retrieve the results of a completed batch request."""
        pending_batches = getattr(self, '_pending_batches', {})

        if batch_id not in pending_batches:
            raise ValueError(f"Batch {batch_id} not found")

        batch_info = pending_batches[batch_id]

        if batch_info['status'] != BatchStatus.COMPLETED.value:
            raise ValueError(f"Batch {batch_id} is not completed yet")

        return batch_info.get('results', [])

    def _list_batches_request(self, limit: int) -> List[BatchResponse]:
        """List all batch requests (simulated)."""
        pending_batches = getattr(self, '_pending_batches', {})

        batch_responses = []
        for batch_id, batch_info in list(pending_batches.items())[:limit]:
            batch_responses.append(BatchResponse(
                id=batch_id,
                status=batch_info['status'],
                created_at=batch_info['created_at'],
                expires_at=batch_info.get(
                    'completed_at', datetime.now(timezone.utc)),
                completed_at=batch_info.get('completed_at'),
                raw_response=f"Batch {batch_id}"
            ))

        return batch_responses

    def _process_batch_if_ready(self, batch_id: str):
        """Process a batch request using concurrent execution."""
        pending_batches = getattr(self, '_pending_batches', {})

        if batch_id not in pending_batches:
            return

        batch_info = pending_batches[batch_id]

        if batch_info['status'] != BatchStatus.IN_PROGRESS.value:
            return

        requests = batch_info['requests']

        try:
            # Process requests concurrently
            results = []
            max_workers = min(10, len(requests))  # Limit concurrent requests

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all requests
                future_to_request = {}
                for request in requests:
                    future = executor.submit(
                        self._make_completion_request, request)
                    future_to_request[future] = request

                # Collect results as they complete
                for future in as_completed(future_to_request):
                    request = future_to_request[future]
                    try:
                        result = future.result()
                        # Add custom_id if it was in the original request
                        if request.custom_id:
                            result.custom_id = request.custom_id
                        results.append(result)
                    except Exception as e:
                        logger.error(
                            f"Request failed in batch {batch_id}: {str(e)}")
                        # Create a failed response
                        failed_result = CompletionResponse(
                            content="",
                            status=CompletionStatus.FAILED.value,
                            model=request.model,
                            input_tokens=0,
                            output_tokens=0,
                            raw_response=str(e),
                            custom_id=request.custom_id
                        )
                        results.append(failed_result)

            # Update batch status
            batch_info['status'] = BatchStatus.COMPLETED.value
            batch_info['completed_at'] = datetime.now(timezone.utc)
            batch_info['results'] = results

            logger.info(
                f"Batch {batch_id} completed with {len(results)} results")

        except Exception as e:
            logger.error(f"Batch {batch_id} failed: {str(e)}")
            batch_info['status'] = BatchStatus.FAILED.value
            batch_info['completed_at'] = datetime.now(timezone.utc)
            batch_info['results'] = []
