from dataclasses import dataclass
from datetime import datetime
import json
import argparse
from typing import Optional, Dict, Any

from db.database import DatabaseManager
from apis.llm.base import BaseAPIManager, BudgetLimitExeption, RateLimitExeption
from apis.llm.data_types import CompletionRequest, CompletionStatus
from apis.llm.anthropic import Anthropic<PERSON>anager
from apis.llm.openai import OpenAIManager

from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import TZ, LLMConfig, extract_article_id, generate_custom_id, process_article

# Configure logging
from utils.logging_config import get_nlp_logger
logger = get_nlp_logger(__name__, log_file=f'llm/llm_analyzer-{datetime.now().strftime(' % Y-%m-%d-%H-%M-%S')}.log')


@dataclass
class APIConfig:
    """Configuration for a specific API processor."""
    # Budget and rate limiting
    total_budget: float = 0.0
    requests_per_minute: int = 30

    # LLM-specific configuration
    llm_config: Optional[LLMConfig] = None


class LLMAnalyzer:
    """
    Main class for analyzing news articles using LLM APIs.

    Provides both single article analysis and batch processing capabilities
    with proper error handling, caching, and structured output storage.
    """

    def __init__(self, api_name: str, propmt_type: str, api_config: APIConfig):
        """Initialize the news analyzer with configuration."""
        self.config = api_config
        self.db = DatabaseManager()
        self.prompt_manager = PromptManager()
        # Initialize LLM API
        self.api_name = api_name
        self.propmt_type = propmt_type
        self.llm_api = self._initialize_llm_api()

        logger.info("LLMAnalyzer initialized successfully")

    def _initialize_llm_api(self) -> BaseAPIManager:
        """Initialize the appropriate LLM API based on configuration."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        api_class = api_mapping.get(self.api_name)
        if not api_class:
            raise ValueError(f"Unsupported API: {self.api_name}")

        current_cost = self.db.llm_api_service.get_total_cost(
            api=self.api_name, prompt_type=self.propmt_type)

        llm_api = api_class(
            total_budget=self.config.total_budget,
            total_cost=current_cost,
            requests_per_minute=self.config.requests_per_minute
        )

        logger.info(f"Initialized {self.api_name} API with: "
                    f"budget {self.config.total_budget}, "
                    f"current cost {current_cost}")
        return llm_api

    def save_completion(self, result_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": self.api_name,
                "prompt_type": self.propmt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "raw_response": result_dict.get('raw_response')
            }
            # if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
            #     db_record['prompt'] = result_dict['system_prompt'] + \
            #         result_dict['user_prompt']

            if 'cost' in result_dict:
                db_record['cost'] = result_dict['cost']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], self.propmt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {self.propmt_type: db_record['content']}):
                    logger.info(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.info(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return {}

    def analyze_article(self, article: Dict[str, Any], custom_id: Optional[str] = None) -> Dict[str, Any]:
        """Analyze a single article using the specified prompt type."""
        try:
            min_input = self.config.llm_config.min_input
            max_input = self.config.llm_config.max_input
            # Process article input
            article_input = process_article(article, min_input, max_input)
            if not article_input:
                logger.error(f"Skipped short article {article['id']}")
                return {}

            llm_config = self.config.llm_config
            # Get and format prompt
            prompt = self.prompt_manager.get_prompt(self.propmt_type)
            system_prompt = prompt['system_prompt']
            formatted_prompt = prompt['prompt_template'].format(
                title=article_input['title'],
                content=article_input['content']
            )

            request = CompletionRequest(
                max_tokens=llm_config.max_tokens,
                temperature=llm_config.temperature,
                user_prompt=formatted_prompt,
                system_prompt=system_prompt,
                model=llm_config.model
            )

            # Call LLM API
            completion = self.llm_api.get_completion(request)

            if not completion or completion.status != CompletionStatus.SUCCEEDED.value or not completion.content:
                logger.error(
                    f"Failed to get completion for article {article['id']}")
                return {}

            completion_dict = completion.to_dict()
            completion_dict['custom_id'] = custom_id or generate_custom_id(
                article['id'], self.propmt_type, self.api_name)
            completion_dict['user_prompt'] = formatted_prompt
            completion_dict['system_prompt'] = system_prompt

            saved_record = self.save_completion(completion_dict)
            if saved_record:
                logger.info(
                    f"Successfully saved result: {completion.custom_id}")
            else:
                logger.error(f"Failed to save result: {completion.custom_id}")

            return saved_record

        except BudgetLimitExeption as e:
            logger.error(f"Budget limit reached: {e}")
        except RateLimitExeption as e:
            logger.error(f"Rate limit reached: {e}")

        except Exception as e:
            logger.error(
                f"Error analyzing article {article.get('id', 'unknown')}: {e}")
        return {}

    def analyze_article_by_url(self, url: str, override: bool = False) -> Dict[str, Any]:
        """Analyze a specific article by URL."""
        try:
            article = self.db.article_service.get_article_by_url(url)
            if not article:
                logger.warning(f"Article not found: {url}")
                return {}

            cid = generate_custom_id(
                article['id'], self.propmt_type, self.api_name)

            # Check if already analyzed
            if not override:
                existing_result = self.db.llm_api_service.get_result_by_id(cid)
                if existing_result:
                    logger.info(f"Returning existing result for {url}")
                    return {self.propmt_type: existing_result['content']}

            return self.analyze_article(article, cid)

        except Exception as e:
            logger.error(f"Error analyzing article by URL {url}: {e}")

        return {}


def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    # Basic options
    parser.add_argument('-u', '--url', help='URL of article to analyze')
    parser.add_argument('-a', '--api', default='openai', help='LLM API name')
    parser.add_argument(
        '-m', '--model', default='gpt-4.1-nano', help='LLM model to use')
    parser.add_argument('-t', '--prompt-type',
                        default='influence_tagging', help='Type of analysis to perform')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=1.0,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=1024,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=110,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=30,
                        help='Rate limit per minute')
    parser.add_argument('--temperature', type=float, default=1.0,
                        help='Temperature for LLM output')
    parser.add_argument('--override', action='store_true',
                        help='Override existing results')

    args = parser.parse_args()

    try:
        # Initialize analyzer

        api_config = APIConfig(
            total_budget=args.budget,
            requests_per_minute=args.requests_per_minute,
            llm_config=LLMConfig(
                model=args.model,
                max_tokens=args.max_tokens,
                max_input=args.max_input,
                min_input=args.min_input,
                temperature=args.temperature
            )
        )
        analyzer = LLMAnalyzer(args.api, args.prompt_type, api_config)

        result = analyzer.analyze_article_by_url(args.url, args.override)
        if result:
            print(json.dumps(result, indent=2))
        else:
            print(f"Analysis failed for URL: {args.url}")

    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    main()
