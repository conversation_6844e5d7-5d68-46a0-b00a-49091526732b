import json
import re
from typing import Dict, Any, List, Tuple

PROMPT_SHORT_NAME = {
    "influence": "INF",
    "influence_tagging": "ITG",
    "influence_scaled": "INS",
    "tagging": "TAG",
    "sentiment": "SEN",
    "comprehensive": "COM"
}


class PromptManager:
    """
    Manages different types of prompts for article analysis.
    Each prompt type has an associated parser to structure the results.
    """

    # System prompts for different analysis types
    SYSTEM_PROMPTS = {
        "influence_tagging": """You are a financial news analyst. Analyze the article and provide:

    1. Category: one of [Market Summary, Macro Market News, Company News, Sector News, Breaking News, Analyst & Opinion]
    2. Regions: geographic focus (e.g., US, Global, China, Europe, Japan)
    3. Tags: comma-separated list of relevant companies, sectors, and macroeconomic terms (e.g., Tesla, semiconductors, inflation, interest rates)
    4. Whether does this article contain news likely to influence the direction of the US stock market (e.g., S&P 500, Nasdaq, Dow). Consider factors such as:
Macroeconomic indicators (e.g., CPI, jobs report, GDP)
Federal Reserve or central bank policies
Key corporate earnings or guidance
Significant geopolitical or regulatory developments

If yes, specify whether the likely impact is bullish or bearish, along with a brief explanation within 100 words. Rate the influence on a scale from -5 to 5, where -5 is most bearish and 5 is most bullish.
If not relevant, respond No.

Respond strictly in the following format:
Category: <...>
Regions: <up to 3 comma-separated list>
Tags: <up to 5 comma-separated list>
Relevant: Yes/No
    <if relevant is Yes>
    Influence: <-5 to 5 rating>
    Reason: <brief explanation>""",
        "influence_scaled": """Does this article contain news likely to influence the direction of the US stock market (e.g., S&P 500, Nasdaq, Dow)?
Consider factors such as:

Macroeconomic indicators (e.g., CPI, jobs report, GDP)
Federal Reserve or central bank policies
Key corporate earnings or guidance
Significant geopolitical or regulatory developments

If yes, specify whether the likely impact is bullish or bearish, along with a brief explanation within 100 words. Rate the influence on a scale from -5 to 5, where -5 is most bearish and 5 is most bullish.

Respond strictly in the following format:
Relevant: Yes
Influence: <rating>
Reason: <brief explanation>

If not relevant, respond with:
Relevant: No""",
        "influence": """Does this article contain news that is likely to influence the direction of the US stock market (e.g., SP500, Nasdaq, Dow)?More actions
Consider factors such as:

Macroeconomic indicators (e.g., CPI, jobs report, GDP)
Fed or central bank policy
Major corporate earnings or guidance
Significant geopolitical or regulatory developments

If yes, also specify whether the influence is likely bullish or bearish for the US market, and briefly explain why.

Respond strictly in the following format:

Relevant: Yes  
Influence: one of [Bullish, Slightly Bullish, Neutral, Slightly Bearish, Bearish]  
Reason: <short explanation>

If not relevant, respond with:
Relevant: No""",
        "tagging": """You are a financial news assistant. Analyze the article and provide:
1. Category: one of [Market Digest, Macro Market News, Company News, Sector News, Breaking News, Analyst & Opinion]
2. Regions: geographic focus (e.g., US, Global, China, Europe)
3. Tags: comma-separated list of relevant companies, sectors, and macroeconomic terms (e.g., Tesla, semiconductors, inflation, interest rates)

Respond strictly in this format:
Category: <...>
Regions: <comma-separated list>
Tags: <comma-separated list>""",
        "sentiment": """You are a financial news assistant. Analyze the article and provide
1. Sentiment: one of [Bullish, Slightly Bullish, Neutral, Slightly Bearish, Bearish]
2. Main Reasons: 1–3 detailed bullet points explaining the sentiment

Respond strictly in this format:
Sentiment: <...>
Main Reasons:
- <reason 1>
- <reason 2>...""",
        "comprehensive": """You are a financial news assistant specializing in market movement analysis. Your primary focus is to identify leading indicators and forward-looking signals that drive market movements, rather than reactive responses. Emphasize predictive catalysts and emerging trends that shape future market direction.
Analysis Instructions
Scope: Analyze the entire article, prioritizing leading economic indicators, forward-looking statements, policy signals, and emerging trends that suggest future market direction rather than past performance.
Focus Areas: Identify predictive signals such as:

Economic data releases and forecasts
Central bank communications and policy shifts
Corporate guidance and forward earnings estimates
Regulatory changes and policy announcements
Technological developments and market innovations
Geopolitical developments with market implications

Required Analysis Format
For each article, provide:
1. Category: one of [Market Digest, Macro Market News, Company News, Sector News, Breaking News, Analyst & Opinion]
2. Regions: up to 3 geographic focuses (e.g., US, Global, China, Europe, Japan, Asia-Pacific)
3. Tags: up to 10 comma-separated list of relevant companies, sectors, leading indicators, and forward-looking terms (e.g., Tesla, semiconductors, PMI, yield curve, Fed guidance, earnings forecasts, GDP projections)
4. Sentiment: one of [Very Bullish, Bullish, Neutral, Bearish, Very Bearish]
5. Main Reasons: 1–3 detailed bullet points explaining the market movement, focusing on:

Leading indicators and predictive signals
Forward-looking policy or economic developments
Anticipated market implications and future trends

Response Format
Respond strictly in this format:
Category: <category>
Regions: <comma-separated list>
Tags: <comma-separated list>
Sentiment: <sentiment>
Main Reasons:
- <leading indicator or predictive signal with forward-looking implications>
- <policy development or economic forecast driving future expectations>
- <emerging trend or anticipated market shift>"""
    }
    PROMPT_TEMPLATES = """Title: {title} 
Content: {content}"""

    @classmethod
    def get_prompt(cls, prompt_type: str) -> Dict[str, str]:
        """
        Get the prompt template and system prompt for a specific analysis type.

        Args:
            prompt_type: Type of analysis to perform

        Returns:
            Dict containing prompt_template and system_prompt
        """
        return {
            "prompt_template": cls.PROMPT_TEMPLATES,
            "system_prompt": cls.SYSTEM_PROMPTS[prompt_type]
        }

    @classmethod
    def get_available_types(cls) -> List[str]:
        """Get list of available prompt types."""
        return list(cls.SYSTEM_PROMPTS.keys())

    @classmethod
    def parse_tagging(cls, completion: str) -> Dict[str, Any]:
        """Parse market analysis results into structured format."""
        result = {
            "category": None,
            "regions": [],
            "tags": [],
        }

        # Extract category
        category_match = re.search(r"Category:\s*(.*?)(?:\n|$)", completion)
        if category_match:
            result["category"] = category_match.group(1).strip()

        # Extract regions
        region_match = re.search(r"Regions:\s*(.*?)(?:\n|$)", completion)
        if region_match:
            result["regions"] = [region.strip()
                                 for region in region_match.group(1).split(",")]

        # Extract tags
        tags_match = re.search(r"Tags:\s*(.*?)(?:\n|$)", completion)
        if tags_match:
            result["tags"] = [tag.strip()
                              for tag in tags_match.group(1).split(",")]

        return result

    @classmethod
    def parse_sentiment(cls, completion: str) -> Dict[str, Any]:
        result = {
            "sentiment": None,
            "reasons": []
        }
        # Extract sentiment
        sentiment_match = re.search(r"Sentiment:\s*(.*?)(?:\n|$)", completion)
        if sentiment_match:
            result["sentiment"] = sentiment_match.group(1).strip()

        # Extract reasons
        reasons_match = re.search(
            r"Main Reasons:(.*?)(?:\n\n|$)", completion, re.DOTALL)
        if reasons_match:
            reasons_text = reasons_match.group(1)
            result["reasons"] = [
                reason.strip().lstrip("- ").strip()
                for reason in reasons_text.split("\n")
                if reason.strip().startswith("-")
            ]

        return result

    @classmethod
    def parse_comprehensive(cls, completion: str) -> Dict[str, Any]:
        result = cls.parse_tagging(completion)
        result.update(cls.parse_sentiment(completion))

        return result

    @classmethod
    def parse_influence(cls, completion: str, scaled: bool = False) -> Dict[str, Any]:
        result = {}
        # Extract relevant
        relevance_match = re.search(r"Relevant:\s*(.*?)(?:\n|$)", completion)
        if relevance_match:
            result["relevant"] = relevance_match.group(1).strip()

        if result["relevant"] == 'Yes':
            # Extract influence
            relevance_match = re.search(
                r"Influence:\s*(.*?)(?:\n|$)", completion)
            if relevance_match:
                result["influence"] = relevance_match.group(1).strip()
                if scaled:
                    result["influence"] = int(result["influence"])

            # Extract reason
            relevance_match = re.search(r"Reason:\s*(.*?)(?:\n|$)", completion)
            if relevance_match:
                result["reason"] = relevance_match.group(1).strip()

        return result

    @classmethod
    def parse_json(cls, completion: str) -> Dict[str, Any]:
        result = {}
        # Extract relevant
        try:
            result = json.loads(completion)
        except Exception as e:
            pass
        return result

    @classmethod
    def parse_influence_scaled(cls, completion: str) -> Dict[str, Any]:
        return cls.parse_influence(completion, scaled=True)

    @classmethod
    def parse_influence_tagging(cls, completion: str) -> Dict[str, Any]:
        result = cls.parse_tagging(completion)
        result.update(cls.parse_influence(completion, scaled=True))
        return result

    @classmethod
    def parse_result(cls, completion: str, prompt_type: str) -> Dict[str, Any]:
        """
        Parse and format the completion based on the prompt type.

        Args:
            completion: Raw completion text from the model
            prompt_type: Type of analysis performed

        Returns:
            Dict containing parsed and formatted results
        """
        parsers = {
            "tagging": cls.parse_tagging,
            "sentiment": cls.parse_sentiment,
            "comprehensive": cls.parse_comprehensive,
            "influence": cls.parse_influence,
            "influence_scaled": cls.parse_influence_scaled,
            "influence_tagging": cls.parse_influence_tagging
        }

        if prompt_type not in parsers:
            return {"raw_completion": completion}

        try:
            return parsers[prompt_type](completion)
        except Exception as e:
            return {"raw_completion": completion, "parse_error": str(e)}
